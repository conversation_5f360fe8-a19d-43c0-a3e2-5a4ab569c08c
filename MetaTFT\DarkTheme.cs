using System;
using System.Drawing;
using System.Windows.Forms;

namespace MetaTFT
{
    public static class DarkTheme
    {
        // Dark theme colors
        public static readonly Color BackgroundColor = Color.FromArgb(32, 32, 32);
        public static readonly Color SurfaceColor = Color.FromArgb(48, 48, 48);
        public static readonly Color PrimaryColor = Color.FromArgb(0, 122, 255);
        public static readonly Color SecondaryColor = Color.FromArgb(64, 64, 64);
        public static readonly Color TextColor = Color.FromArgb(255, 255, 255);
        public static readonly Color TextSecondaryColor = Color.FromArgb(180, 180, 180);
        public static readonly Color BorderColor = Color.FromArgb(80, 80, 80);
        public static readonly Color SuccessColor = Color.FromArgb(40, 167, 69);
        public static readonly Color WarningColor = Color.FromArgb(255, 193, 7);
        public static readonly Color DangerColor = Color.FromArgb(220, 53, 69);

        public static void ApplyToForm(Form form)
        {
            form.BackColor = BackgroundColor;
            form.ForeColor = TextColor;
            
            ApplyToControls(form.Controls);
        }

        private static void ApplyToControls(Control.ControlCollection controls)
        {
            foreach (Control control in controls)
            {
                ApplyToControl(control);
                
                if (control.HasChildren)
                {
                    ApplyToControls(control.Controls);
                }
            }
        }

        private static void ApplyToControl(Control control)
        {
            switch (control)
            {
                case Button button:
                    ApplyToButton(button);
                    break;
                case TextBox textBox:
                    ApplyToTextBox(textBox);
                    break;
                case ListBox listBox:
                    ApplyToListBox(listBox);
                    break;
                case Label label:
                    ApplyToLabel(label);
                    break;
                case Panel panel:
                    ApplyToPanel(panel);
                    break;
                case GroupBox groupBox:
                    ApplyToGroupBox(groupBox);
                    break;
                case NumericUpDown numericUpDown:
                    ApplyToNumericUpDown(numericUpDown);
                    break;
                case CheckBox checkBox:
                    ApplyToCheckBox(checkBox);
                    break;
                default:
                    control.BackColor = BackgroundColor;
                    control.ForeColor = TextColor;
                    break;
            }
        }

        private static void ApplyToButton(Button button)
        {
            button.BackColor = SurfaceColor;
            button.ForeColor = TextColor;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderColor = BorderColor;
            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.MouseOverBackColor = Color.FromArgb(70, 70, 70);
            button.FlatAppearance.MouseDownBackColor = Color.FromArgb(90, 90, 90);
        }

        private static void ApplyToTextBox(TextBox textBox)
        {
            textBox.BackColor = SurfaceColor;
            textBox.ForeColor = TextColor;
            textBox.BorderStyle = BorderStyle.FixedSingle;
        }

        private static void ApplyToListBox(ListBox listBox)
        {
            listBox.BackColor = SurfaceColor;
            listBox.ForeColor = TextColor;
            listBox.BorderStyle = BorderStyle.FixedSingle;
        }

        private static void ApplyToLabel(Label label)
        {
            label.BackColor = Color.Transparent;
            label.ForeColor = TextColor;
        }

        private static void ApplyToPanel(Panel panel)
        {
            panel.BackColor = BackgroundColor;
        }

        private static void ApplyToGroupBox(GroupBox groupBox)
        {
            groupBox.BackColor = Color.Transparent;
            groupBox.ForeColor = TextColor;
        }

        private static void ApplyToNumericUpDown(NumericUpDown numericUpDown)
        {
            numericUpDown.BackColor = SurfaceColor;
            numericUpDown.ForeColor = TextColor;
            numericUpDown.BorderStyle = BorderStyle.FixedSingle;
        }

        private static void ApplyToCheckBox(CheckBox checkBox)
        {
            checkBox.BackColor = Color.Transparent;
            checkBox.ForeColor = TextColor;
        }

        public static Button CreateStyledButton(string text, Color? backgroundColor = null)
        {
            var button = new Button
            {
                Text = text,
                BackColor = backgroundColor ?? SurfaceColor,
                ForeColor = TextColor,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };
            
            button.FlatAppearance.BorderColor = BorderColor;
            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.MouseOverBackColor = Color.FromArgb(70, 70, 70);
            button.FlatAppearance.MouseDownBackColor = Color.FromArgb(90, 90, 90);
            
            return button;
        }

        public static Panel CreateStyledPanel()
        {
            return new Panel
            {
                BackColor = SurfaceColor,
                BorderStyle = BorderStyle.FixedSingle
            };
        }
    }
}
