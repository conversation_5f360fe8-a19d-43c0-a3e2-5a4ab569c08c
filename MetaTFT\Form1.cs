﻿using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MetaTFT
{
    public partial class Form1 : Form
    {
        private MacroEngine macroEngine;
        private ListBox actionListBox;
        private Button startButton;
        private Button stopButton;
        private Button clearButton;
        private CheckBox loopCheckBox;
        private Label statusLabel;
        private Panel controlPanel;
        private Panel actionPanel;

        // Add action controls
        private GroupBox addActionGroup;
        private Button addKeyButton;
        private Button addDelayButton;
        private Button addMouseLeftButton;
        private Button addMouseRightButton;
        private NumericUpDown delayNumeric;
        private NumericUpDown keyNumeric;
        private NumericUpDown mouseXNumeric;
        private NumericUpDown mouseYNumeric;

        // Action management controls
        private Button removeButton;
        private Button moveUpButton;
        private Button moveDownButton;

        public Form1()
        {
            InitializeComponent();
            InitializeMacroEngine();
            SetupUI();
            DarkTheme.ApplyToForm(this);
        }

        private void InitializeMacroEngine()
        {
            macroEngine = new MacroEngine();
            macroEngine.StatusChanged += OnMacroStatusChanged;
            macroEngine.ActionExecuted += OnActionExecuted;
            macroEngine.MacroCompleted += OnMacroCompleted;
        }

        private void SetupUI()
        {
            this.Text = "MetaTFT - TFT Team Comps";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(800, 400);

            CreateControls();
            LayoutControls();
            SetupEventHandlers();
        }

        private void CreateControls()
        {
            // Main panels
            controlPanel = new Panel { Dock = DockStyle.Top, Height = 200 };
            actionPanel = new Panel { Dock = DockStyle.Fill };

            // Action list
            actionListBox = new ListBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Consolas", 9F),
                SelectionMode = SelectionMode.One
            };

            // Control buttons
            startButton = DarkTheme.CreateStyledButton("Start Macro", DarkTheme.SuccessColor);
            stopButton = DarkTheme.CreateStyledButton("Stop Macro", DarkTheme.DangerColor);
            clearButton = DarkTheme.CreateStyledButton("Clear All");
            loopCheckBox = new CheckBox { Text = "Loop Macro", AutoSize = true };

            // Status
            statusLabel = new Label
            {
                Text = "Ready",
                AutoSize = false,
                Height = 25,
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Bottom
            };

            // Add action group
            addActionGroup = new GroupBox { Text = "Add Actions", Height = 120 };

            // Add action buttons
            addKeyButton = DarkTheme.CreateStyledButton("Add Key Press");
            addDelayButton = DarkTheme.CreateStyledButton("Add Delay");
            addMouseLeftButton = DarkTheme.CreateStyledButton("Left");
            addMouseRightButton = DarkTheme.CreateStyledButton("Right");

            // Numeric inputs
            keyNumeric = new NumericUpDown { Minimum = 0, Maximum = 9, Value = 0 };
            delayNumeric = new NumericUpDown { Minimum = 100, Maximum = 10000, Value = 1000, Increment = 100 };
            mouseXNumeric = new NumericUpDown { Minimum = 0, Maximum = 3840, Value = 100 };
            mouseYNumeric = new NumericUpDown { Minimum = 0, Maximum = 2160, Value = 100 };

            // Action management buttons
            removeButton = DarkTheme.CreateStyledButton("Remove", DarkTheme.DangerColor);
            moveUpButton = DarkTheme.CreateStyledButton("Move Up");
            moveDownButton = DarkTheme.CreateStyledButton("Move Down");
        }

        private void LayoutControls()
        {
            // Add controls to form
            this.Controls.Add(actionPanel);
            this.Controls.Add(controlPanel);
            this.Controls.Add(statusLabel);

            // Layout control panel
            var leftPanel = new Panel { Dock = DockStyle.Left, Width = 400 };
            var rightPanel = new Panel { Dock = DockStyle.Fill };

            controlPanel.Controls.Add(rightPanel);
            controlPanel.Controls.Add(leftPanel);

            // Layout add action group
            leftPanel.Controls.Add(addActionGroup);
            addActionGroup.Dock = DockStyle.Fill;

            // Layout add action controls
            var row1 = new Panel { Height = 35, Dock = DockStyle.Top };
            var row2 = new Panel { Height = 35, Dock = DockStyle.Top };
            var row3 = new Panel { Height = 35, Dock = DockStyle.Top };

            addActionGroup.Controls.Add(row3);
            addActionGroup.Controls.Add(row2);
            addActionGroup.Controls.Add(row1);

            // Row 1: Key press
            var keyLabel = new Label { Text = "Key (0-9):", Width = 80, TextAlign = ContentAlignment.MiddleLeft };
            keyNumeric.Width = 60;
            addKeyButton.Width = 100;

            row1.Controls.Add(keyLabel);
            row1.Controls.Add(keyNumeric);
            row1.Controls.Add(addKeyButton);

            keyLabel.Location = new Point(10, 8);
            keyNumeric.Location = new Point(95, 6);
            addKeyButton.Location = new Point(165, 5);

            // Row 2: Delay
            var delayLabel = new Label { Text = "Delay (ms):", Width = 80, TextAlign = ContentAlignment.MiddleLeft };
            delayNumeric.Width = 80;
            addDelayButton.Width = 100;

            row2.Controls.Add(delayLabel);
            row2.Controls.Add(delayNumeric);
            row2.Controls.Add(addDelayButton);

            delayLabel.Location = new Point(10, 8);
            delayNumeric.Location = new Point(95, 6);
            addDelayButton.Location = new Point(185, 5);

            // Row 3: Mouse clicks
            var mouseLabel = new Label { Text = "Mouse X,Y:", Width = 80, TextAlign = ContentAlignment.MiddleLeft };
            mouseXNumeric.Width = 60;
            mouseYNumeric.Width = 60;
            addMouseLeftButton.Width = 80;
            addMouseRightButton.Width = 80;

            row3.Controls.Add(mouseLabel);
            row3.Controls.Add(mouseXNumeric);
            row3.Controls.Add(mouseYNumeric);
            row3.Controls.Add(addMouseLeftButton);
            row3.Controls.Add(addMouseRightButton);

            mouseLabel.Location = new Point(10, 8);
            mouseXNumeric.Location = new Point(95, 6);
            mouseYNumeric.Location = new Point(165, 6);
            addMouseLeftButton.Location = new Point(235, 5);
            addMouseRightButton.Location = new Point(320, 5);

            // Layout right panel (control buttons)
            var buttonPanel = new Panel { Height = 120, Dock = DockStyle.Top };
            rightPanel.Controls.Add(buttonPanel);

            startButton.Size = new Size(100, 30);
            stopButton.Size = new Size(100, 30);
            clearButton.Size = new Size(100, 30);
            removeButton.Size = new Size(100, 30);
            moveUpButton.Size = new Size(100, 30);
            moveDownButton.Size = new Size(100, 30);

            startButton.Location = new Point(10, 10);
            stopButton.Location = new Point(120, 10);
            clearButton.Location = new Point(230, 10);

            removeButton.Location = new Point(10, 50);
            moveUpButton.Location = new Point(120, 50);
            moveDownButton.Location = new Point(230, 50);

            loopCheckBox.Location = new Point(10, 90);

            buttonPanel.Controls.Add(startButton);
            buttonPanel.Controls.Add(stopButton);
            buttonPanel.Controls.Add(clearButton);
            buttonPanel.Controls.Add(removeButton);
            buttonPanel.Controls.Add(moveUpButton);
            buttonPanel.Controls.Add(moveDownButton);
            buttonPanel.Controls.Add(loopCheckBox);

            // Layout action panel
            actionPanel.Controls.Add(actionListBox);
        }

        private void SetupEventHandlers()
        {
            // Add action button events
            addKeyButton.Click += (s, e) => AddKeyAction();
            addDelayButton.Click += (s, e) => AddDelayAction();
            addMouseLeftButton.Click += (s, e) => AddMouseAction(true);
            addMouseRightButton.Click += (s, e) => AddMouseAction(false);

            // Control button events
            startButton.Click += async (s, e) => await StartMacroAsync();
            stopButton.Click += (s, e) => StopMacro();
            clearButton.Click += (s, e) => ClearActions();

            // Action management events
            removeButton.Click += (s, e) => RemoveSelectedAction();
            moveUpButton.Click += (s, e) => MoveSelectedActionUp();
            moveDownButton.Click += (s, e) => MoveSelectedActionDown();

            // List selection events
            actionListBox.SelectedIndexChanged += (s, e) => UpdateButtonStates();

            // Form events
            this.FormClosing += (s, e) => StopMacro();
        }

        private void AddKeyAction()
        {
            int keyValue = (int)keyNumeric.Value;
            var action = new MacroAction(MacroActionType.KeyPress, $"Key Press {keyValue}", keyValue);
            macroEngine.AddAction(action);
            RefreshActionList();
        }

        private void AddDelayAction()
        {
            int delayMs = (int)delayNumeric.Value;
            var action = new MacroAction(MacroActionType.Delay, $"Delay {delayMs}ms", delayMs);
            macroEngine.AddAction(action);
            RefreshActionList();
        }

        private void AddMouseAction(bool isLeftClick)
        {
            int x = (int)mouseXNumeric.Value;
            int y = (int)mouseYNumeric.Value;
            var actionType = isLeftClick ? MacroActionType.MouseLeftClick : MacroActionType.MouseRightClick;
            var action = new MacroAction(actionType, $"Mouse {(isLeftClick ? "Left" : "Right")} Click", 0, x, y);
            macroEngine.AddAction(action);
            RefreshActionList();
        }

        private async Task StartMacroAsync()
        {
            if (macroEngine.Actions.Count == 0)
            {
                MessageBox.Show("Please add some actions first!", "No Actions", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            bool loop = loopCheckBox.Checked;
            await macroEngine.StartMacroAsync(loop);
        }

        private void StopMacro()
        {
            macroEngine.StopMacro();
        }

        private void ClearActions()
        {
            if (macroEngine.IsRunning)
            {
                MessageBox.Show("Cannot clear actions while macro is running!", "Macro Running", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            macroEngine.ClearActions();
            RefreshActionList();
        }

        private void RemoveSelectedAction()
        {
            if (macroEngine.IsRunning)
            {
                MessageBox.Show("Cannot remove actions while macro is running!", "Macro Running", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int selectedIndex = actionListBox.SelectedIndex;
            if (selectedIndex >= 0)
            {
                macroEngine.RemoveAction(selectedIndex);
                RefreshActionList();
            }
        }

        private void MoveSelectedActionUp()
        {
            if (macroEngine.IsRunning) return;

            int selectedIndex = actionListBox.SelectedIndex;
            if (selectedIndex > 0)
            {
                macroEngine.MoveActionUp(selectedIndex);
                RefreshActionList();
                actionListBox.SelectedIndex = selectedIndex - 1;
            }
        }

        private void MoveSelectedActionDown()
        {
            if (macroEngine.IsRunning) return;

            int selectedIndex = actionListBox.SelectedIndex;
            if (selectedIndex >= 0 && selectedIndex < macroEngine.Actions.Count - 1)
            {
                macroEngine.MoveActionDown(selectedIndex);
                RefreshActionList();
                actionListBox.SelectedIndex = selectedIndex + 1;
            }
        }

        private void RefreshActionList()
        {
            actionListBox.Items.Clear();
            for (int i = 0; i < macroEngine.Actions.Count; i++)
            {
                actionListBox.Items.Add($"{i + 1:D2}. {macroEngine.Actions[i]}");
            }
            UpdateButtonStates();
        }

        private void UpdateButtonStates()
        {
            bool isRunning = macroEngine.IsRunning;
            bool hasActions = macroEngine.Actions.Count > 0;
            bool hasSelection = actionListBox.SelectedIndex >= 0;

            // Control buttons
            startButton.Enabled = !isRunning && hasActions;
            stopButton.Enabled = isRunning;
            clearButton.Enabled = !isRunning && hasActions;

            // Add action buttons
            addKeyButton.Enabled = !isRunning;
            addDelayButton.Enabled = !isRunning;
            addMouseLeftButton.Enabled = !isRunning;
            addMouseRightButton.Enabled = !isRunning;

            // Action management buttons
            removeButton.Enabled = !isRunning && hasSelection;
            moveUpButton.Enabled = !isRunning && hasSelection && actionListBox.SelectedIndex > 0;
            moveDownButton.Enabled = !isRunning && hasSelection && actionListBox.SelectedIndex < macroEngine.Actions.Count - 1;

            // Loop checkbox
            loopCheckBox.Enabled = !isRunning;
        }

        // Macro engine event handlers
        private void OnMacroStatusChanged(object sender, string status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnMacroStatusChanged(sender, status)));
                return;
            }

            statusLabel.Text = $"Status: {status}";
            UpdateButtonStates();
        }

        private void OnActionExecuted(object sender, int actionIndex)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnActionExecuted(sender, actionIndex)));
                return;
            }

            // Highlight the currently executing action
            if (actionIndex >= 0 && actionIndex < actionListBox.Items.Count)
            {
                actionListBox.SelectedIndex = actionIndex;
            }
        }

        private void OnMacroCompleted(object sender, EventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnMacroCompleted(sender, e)));
                return;
            }

            actionListBox.ClearSelected();
            UpdateButtonStates();
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            UpdateButtonStates();
        }
    }
}
