<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAHCAAAAEAIACoDgAAFgAAACgAAAAcAAAAQAAAAAEAIAAAAAAAAA4AAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABstcsAbLTLAGy0yxZss8p/bLPK5Wyz
        yuNss8p+bLTLFWy0ywBstcsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAGq0ywBrs8oAarHHCGy0y05stMvAbLTL+my0y/9stMv/bLTL+my0
        y71stMtKbLTLB2y0ywBrs8wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAGy0ywBstMsBbLTLL2yzyqFstMvzbLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL8muz
        yqFrs8otbrbOAWy0ywAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAbLTLAGy0
        ywBstMsbbLTLfmy0y+NstMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL4Wy0
        y3tstMsabLTLAGy0ywAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABstcwAbLTLAGy0ywtstMtbbLTLzmy0
        y/1stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv9bLTLy2uy
        yVlpr8YLaa/FAGuzywAAAAAAAAAAAAAAAABstMsAbLTLA2y0yzpstMutbLTL92y0y/9stMv/bLTL/2y0
        y/9stMv/bLTL/2y0y/9ut87/bbbN/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv3bLTLrWy0
        yztstMsDbLTLAAAAAABrtMoAbLTLI2y0y45stMvqbLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0
        y/9orcP+SnqK/FaPof1stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMvrbLTLjGy0
        yyFstMsAbLTLUWy0y9RstMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9tts7/U4mb/QkN
        D/4fMjn8aK3D/my0zP9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMvRbLTLRmy0
        y8ZstMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/brfO/1CFlv0HCgv+HC0z/Wes
        wv5stcz/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y7ZstMvWbLTL/2y0
        y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/222zv9UjJ79CQ0O/iA0O/1pr8X+bLTL/2y0
        y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMvIbLTL12y0y/9stMv/bLTL/2y0
        y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9tts3/WJKl/QoPEf4kO0L9arHI/my0y/9stMv/bLTL/2y0
        y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTLymy0y9dstMv/bLTL/2y0y/9stMv/bLTL/2y0
        y/9stMv/bLTL/2y0y/9stMv/bbbN/1uYq/0NExX+KEJL/Wuzyv5stMv/bLTL/2y0y/9stMv/bLTL/2y0
        y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y8tstMvYbLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0
        y/9stMv/bLTL/221zf9enbH9Dxca/S1KU/1stMz/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0
        y/9stMv/bLTL/2y0y/9stMvLbLTL2Gy0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0
        y/9ttcz/YaK2/RIcH/0yUVz8bbbN/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0
        y/9stMv/bLTLzGy0y9hstMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bbXM/2Sm
        u/4VISb9Nlll/G22zv9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0
        y8xstMvZbLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y1zP9mqsD+GSgt/Tth
        bvxut87/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMvNbLTL2Gy0
        y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9ttcz/ZKa7/RUhJv01WGP8bbbO/2y0
        y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTLzWy0y9hstMv/bLTL/2y0
        y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/brfO/0l5ifwIDA3+GSgt/WGit/1tts3/bLTL/2y0
        y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y8xstMvYbLTL/2y0y/9stMv/bLTL/2y0
        y/9stMv/bLTL/2y0y/9stMv/bbbN/1iSpf0VISb9BQYH/wYHCP8tSlT8aK3D/my1zP9stMv/bLTL/2y0
        y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMvMbLTL2Gy0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0
        y/9stMv/bLXM/2Snvf4gMzr9BAUF/wUHCP8FBwj/BggJ/j5mc/xstcz/bLTL/2y0y/9stMv/bLTL/2y0
        y/9stMv/bLTL/2y0y/9stMv/bLTLy2y0y9dstMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/263
        zv9Sh5n9DBIV/Q8WGf0fMjn9FCAk/g4VGP0fMjj9aK3E/my0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0
        y/9stMv/bLTL/2y0y8tstMvXbLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9ut87/TH6O/Rkp
        Lv01V2L7OFxo/StGT/03WmX7Gysw/Wapv/5stcz/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0
        y/9stMvJbLTL1Wy0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/brfO/0x+j/0cLDL9OmBs/jdb
        Z/0sSFH+O2Ju/BoqMPxlqb79bLXM/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTLx2y0
        y75stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/263zv9OgpP8HC0z/TpgbP43W2f9LEdQ/jth
        bf0dLjT9Z6vB/my1zP9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y6pstMs8bLTLwWy0
        y/xstMv/bLTL/2y0y/9stMv/bLTL/2y0y/9tts7/U4qb/R0uNPw4XGj8Nlpl/CtGT/05Xmr8Ijc+/Gmv
        xf5stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL+my0y7RstMsxarDHAGuyyRRstMt0bLTL3Wy0
        y/9stMv/bLTL/2y0y/9stMv/bbXN/16dsf0cLTP8FyUp/BgnLPwTHiL8GSgt/DFRXPxstMr/bLTL/2y0
        y/9stMv/bLTL/2y0y/9stMv+bLTL1Wy0y2ZstMsObLTLAAAAAABstMsAbLTLAWy0yyhstMuSbLTL7my0
        y/9stMv/bLTL/2y0y/9rs8r/W5eq/Ut9jP1GdIL7R3aF/E+Elf1jpbr+bLXM/2y0y/9stMv/bLTL/2y0
        y/9stMvobLTLhmuzySBPhpcAbLTLAAAAAAAAAAAAAAAAAAAAAABstMsAbLTLBGy0y0RstMu3bLTL+Wy0
        y/9stMv/bLTL/222zf9ut87/brfO/263zv9ut87/bLXM/2y0y/9stMv/bLTL/2y0y/dstMutbLTLOGy0
        ywJstMsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGy1ywBstMsAbLTLDmy0y2NstMvQbLTL/Wy0
        y/9stMv/bLTL/2y0y/9stMv/bLTL/2y0y/9stMv/bLTL/Wy0y8pstMtZbLTLC2y0ywBrsccAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGy0ywBrssoAbLTLG2uyyYVss8robLTL/2y0
        y/9stMv/bLTL/2y0y/9stMv/bLTL4my0y3lstMsXbLTLAGy0ywAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGOlugBfnrICa7PJNmy0y6dstMvzbLTL/2y0
        y/9stMvxbLTLnmy0yy9stMsBbLTLAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAbLPKAGy0ywBstcwKa7PKXGuzycNrs8q+bLPKVmy0
        ywhstMsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP/gf/D/gB/w/gAH8PwA
        A/DwAADwwAAAMIAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAAAAQwAAAcPAAAPD8AAPw/wAP8P+AH/D/4H/w
</value>
  </data>
</root>