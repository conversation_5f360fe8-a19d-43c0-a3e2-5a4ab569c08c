using System;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace MetaTFT
{
    public class InterceptionWrapper
    {
        // -------- Interception DLL imports --------
        [DllImport("interception.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern IntPtr interception_create_context();

        [DllImport("interception.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern void interception_destroy_context(IntPtr context);

        [DllImport("interception.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern int interception_send(IntPtr context, int device, ref Stroke stroke, int n);

        [DllImport("interception.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern int interception_wait(IntPtr context);

        [DllImport("interception.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern int interception_receive(IntPtr context, int device, ref Stroke stroke, int n);

        [DllImport("interception.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern int interception_is_mouse(int device);

        [DllImport("interception.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern int interception_is_keyboard(int device);

        // -------- User32 imports --------
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool GetCursorPos(out POINT lpPoint);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool SetCursorPos(int x, int y);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool ClientToScreen(IntPtr hWnd, ref POINT lpPoint);

        // -------- Structs --------
        [StructLayout(LayoutKind.Sequential)]
        public struct POINT
        {
            public int X;
            public int Y;

            public POINT(int x, int y)
            {
                X = x;
                Y = y;
            }
        }

        [StructLayout(LayoutKind.Sequential)]
        struct MouseStroke
        {
            public ushort state;
            public ushort flags;
            public short rolling;
            public int x;
            public int y;
            public uint information;
        }

        [StructLayout(LayoutKind.Explicit)]
        struct Stroke
        {
            [FieldOffset(0)] public MouseStroke mouse;
            [FieldOffset(0)] public KeyStroke key;
        }

        [StructLayout(LayoutKind.Sequential)]
        struct KeyStroke
        {
            public ushort code;   // Scan code
            public ushort state;  // 0 = down, 1 = up
            public uint information;
        }

        // -------- Constants --------
        const ushort KEY_DOWN = 0;
        const ushort KEY_UP = 1;
        const ushort MOUSE_LEFT_BUTTON_DOWN = 0x01;
        const ushort MOUSE_LEFT_BUTTON_UP = 0x02;
        const ushort MOUSE_RIGHT_BUTTON_DOWN = 0x04;
        const ushort MOUSE_RIGHT_BUTTON_UP = 0x08;

        // -------- Fields --------
        private int foundKeyboardDevice = -1;
        private int foundMouseDevice = -1;

        public int GetFirstKeyboardDevice()
        {
            IntPtr context = interception_create_context();
            int foundDevice = -1;

            // Interception device range is 1..20 (1–10 = keyboards, 11–20 = mice)
            for (int device = 1; device <= 20; device++)
            {
                if (interception_is_keyboard(device) != 0)
                {
                    foundDevice = device;
                    break; // take first keyboard
                }
            }

            interception_destroy_context(context);
            foundKeyboardDevice = foundDevice;
            return foundDevice;
        }

        public int GetFirstMouseDevice()
        {
            IntPtr context = interception_create_context();
            int foundDevice = -1;

            // Interception device range is 1..20 (1–10 = keyboards, 11–20 = mice)
            for (int device = 1; device <= 20; device++)
            {
                if (interception_is_mouse(device) != 0)
                {
                    foundDevice = device;
                    break; // take first mouse
                }
            }

            interception_destroy_context(context);
            foundMouseDevice = foundDevice;
            return foundDevice;
        }

        private int GetScanCodeFromValue(int value)
        {
            // Map number keys 0-9 to their scan codes
            switch (value)
            {
                case 0: return 11; // 0 key
                case 1: return 2;  // 1 key
                case 2: return 3;  // 2 key
                case 3: return 4;  // 3 key
                case 4: return 5;  // 4 key
                case 5: return 6;  // 5 key
                case 6: return 7;  // 6 key
                case 7: return 8;  // 7 key
                case 8: return 9;  // 8 key
                case 9: return 10; // 9 key
                default: return value; // Return as-is for other values
            }
        }

        public bool SendKeyPress(int keyValue)
        {
            if (foundKeyboardDevice < 0)
                foundKeyboardDevice = GetFirstKeyboardDevice();

            if (foundKeyboardDevice < 0)
            {
                MessageBox.Show("No keyboard device found! Install interception driver and restart the PC", "Macro Error");
                return false;
            }

            new Thread(() =>
            {
                try
                {
                    IntPtr context = interception_create_context();
                    Stroke stroke = new Stroke();
                    
                    // Key down
                    stroke.key.code = (ushort)GetScanCodeFromValue(keyValue);
                    stroke.key.state = KEY_DOWN;
                    interception_send(context, foundKeyboardDevice, ref stroke, 1);

                    Thread.Sleep(50); // Small delay between down and up

                    // Key up
                    stroke.key.code = (ushort)GetScanCodeFromValue(keyValue);
                    stroke.key.state = KEY_UP;
                    interception_send(context, foundKeyboardDevice, ref stroke, 1);

                    interception_destroy_context(context);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error sending key press: {ex.Message}", "Macro Error");
                }
            }).Start();

            return true;
        }

        public bool SendMouseClick(int x, int y, bool isLeftClick = true)
        {
            if (foundMouseDevice < 0)
                foundMouseDevice = GetFirstMouseDevice();

            if (foundMouseDevice < 0)
            {
                MessageBox.Show("No mouse device found! Install interception driver and restart the PC", "Macro Error");
                return false;
            }

            new Thread(() =>
            {
                try
                {
                    // Save current cursor position
                    POINT originalPos;
                    GetCursorPos(out originalPos);

                    // Move cursor to target position
                    SetCursorPos(x, y);
                    Thread.Sleep(100);

                    // Send click via Interception
                    IntPtr context = interception_create_context();
                    Stroke stroke = new Stroke();

                    if (isLeftClick)
                    {
                        stroke.mouse.state = MOUSE_LEFT_BUTTON_DOWN;
                        interception_send(context, foundMouseDevice, ref stroke, 1);

                        Thread.Sleep(50);

                        stroke.mouse.state = MOUSE_LEFT_BUTTON_UP;
                        interception_send(context, foundMouseDevice, ref stroke, 1);
                    }
                    else
                    {
                        stroke.mouse.state = MOUSE_RIGHT_BUTTON_DOWN;
                        interception_send(context, foundMouseDevice, ref stroke, 1);

                        Thread.Sleep(50);

                        stroke.mouse.state = MOUSE_RIGHT_BUTTON_UP;
                        interception_send(context, foundMouseDevice, ref stroke, 1);
                    }

                    interception_destroy_context(context);

                    Thread.Sleep(100);
                    // Restore cursor to original position
                    SetCursorPos(originalPos.X, originalPos.Y);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error sending mouse click: {ex.Message}", "Macro Error");
                }
            }).Start();

            return true;
        }
    }
}
