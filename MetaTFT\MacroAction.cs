using System;

namespace MetaTFT
{
    public enum MacroActionType
    {
        Key<PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON>lick,
        MouseR<PERSON><PERSON>lick,
        Delay
    }

    public class MacroAction
    {
        public MacroActionType Type { get; set; }
        public string Description { get; set; }
        public int Value { get; set; } // For key codes or delay time
        public int X { get; set; } // For mouse coordinates
        public int Y { get; set; } // For mouse coordinates

        public MacroAction(MacroActionType type, string description, int value = 0, int x = 0, int y = 0)
        {
            Type = type;
            Description = description;
            Value = value;
            X = x;
            Y = y;
        }

        public override string ToString()
        {
            switch (Type)
            {
                case MacroActionType.KeyPress:
                    return $"Key Press: {GetKeyName(Value)}";
                case MacroActionType.MouseLeftClick:
                    return $"Mouse Left Click: ({X}, {Y})";
                case MacroActionType.MouseRightClick:
                    return $"Mouse Right Click: ({X}, {Y})";
                case MacroActionType.Delay:
                    return $"Delay: {Value}ms";
                default:
                    return Description;
            }
        }

        private string Get<PERSON><PERSON><PERSON><PERSON>(int scanCode)
        {
            switch (scanCode)
            {
                case 11: return "0";
                case 2: return "1";
                case 3: return "2";
                case 4: return "3";
                case 5: return "4";
                case 6: return "5";
                case 7: return "6";
                case 8: return "7";
                case 9: return "8";
                case 10: return "9";
                default: return $"Key {scanCode}";
            }
        }
    }
}
