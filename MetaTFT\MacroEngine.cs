using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace MetaTFT
{
    public class MacroEngine
    {
        private readonly InterceptionWrapper interception;
        private readonly List<MacroAction> actions;
        private bool isRunning;
        private CancellationTokenSource cancellationTokenSource;

        public event EventHandler<string> StatusChanged;
        public event EventHandler<int> ActionExecuted;
        public event EventHandler MacroCompleted;

        public bool IsRunning => isRunning;
        public List<MacroAction> Actions => actions;

        public MacroEngine()
        {
            interception = new InterceptionWrapper();
            actions = new List<MacroAction>();
        }

        public void AddAction(MacroAction action)
        {
            actions.Add(action);
        }

        public void RemoveAction(int index)
        {
            if (index >= 0 && index < actions.Count)
            {
                actions.RemoveAt(index);
            }
        }

        public void ClearActions()
        {
            actions.Clear();
        }

        public void MoveActionUp(int index)
        {
            if (index > 0 && index < actions.Count)
            {
                var action = actions[index];
                actions.RemoveAt(index);
                actions.Insert(index - 1, action);
            }
        }

        public void MoveActionDown(int index)
        {
            if (index >= 0 && index < actions.Count - 1)
            {
                var action = actions[index];
                actions.RemoveAt(index);
                actions.Insert(index + 1, action);
            }
        }

        public async Task StartMacroAsync(bool loop = false)
        {
            if (isRunning || actions.Count == 0)
                return;

            isRunning = true;
            cancellationTokenSource = new CancellationTokenSource();
            
            OnStatusChanged("Macro started");

            try
            {
                do
                {
                    for (int i = 0; i < actions.Count; i++)
                    {
                        if (cancellationTokenSource.Token.IsCancellationRequested)
                        {
                            OnStatusChanged("Macro cancelled");
                            return;
                        }

                        var action = actions[i];
                        OnActionExecuted(i);
                        OnStatusChanged($"Executing: {action}");

                        await ExecuteActionAsync(action);

                        if (cancellationTokenSource.Token.IsCancellationRequested)
                        {
                            OnStatusChanged("Macro cancelled");
                            return;
                        }
                    }
                } while (loop && !cancellationTokenSource.Token.IsCancellationRequested);

                OnStatusChanged("Macro completed");
                OnMacroCompleted();
            }
            catch (Exception ex)
            {
                OnStatusChanged($"Macro error: {ex.Message}");
            }
            finally
            {
                isRunning = false;
                cancellationTokenSource?.Dispose();
                cancellationTokenSource = null;
            }
        }

        public void StopMacro()
        {
            if (isRunning && cancellationTokenSource != null)
            {
                cancellationTokenSource.Cancel();
                OnStatusChanged("Stopping macro...");
            }
        }

        private async Task ExecuteActionAsync(MacroAction action)
        {
            switch (action.Type)
            {
                case MacroActionType.KeyPress:
                    interception.SendKeyPress(action.Value);
                    // Wait a bit for the key press to complete
                    await Task.Delay(100);
                    break;

                case MacroActionType.MouseLeftClick:
                    interception.SendMouseClick(action.X, action.Y, true);
                    // Wait a bit for the click to complete
                    await Task.Delay(200);
                    break;

                case MacroActionType.MouseRightClick:
                    interception.SendMouseClick(action.X, action.Y, false);
                    // Wait a bit for the click to complete
                    await Task.Delay(200);
                    break;

                case MacroActionType.Delay:
                    await Task.Delay(action.Value, cancellationTokenSource.Token);
                    break;
            }
        }

        protected virtual void OnStatusChanged(string status)
        {
            StatusChanged?.Invoke(this, status);
        }

        protected virtual void OnActionExecuted(int actionIndex)
        {
            ActionExecuted?.Invoke(this, actionIndex);
        }

        protected virtual void OnMacroCompleted()
        {
            MacroCompleted?.Invoke(this, EventArgs.Empty);
        }
    }
}
