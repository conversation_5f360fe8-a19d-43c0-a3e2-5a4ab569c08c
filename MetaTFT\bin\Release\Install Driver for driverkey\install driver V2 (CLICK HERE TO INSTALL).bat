@echo off

:Admin
cd /d "%~dp0"
echo Running elevated in: %cd%

:: Check if exe exists
if not exist "install-interception.exe" (
    echo ERROR: install-interception.exe not found in "%~dp0"
    echo Place the .bat in the SAME folder as install-interception.exe
    echo Or edit this script to point to full path.
    pause
    exit /b
)

:: Run installer
"install-interception.exe" /install

echo.
pause
