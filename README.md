# Modern Macro - Interception

A modern, dark-themed macro program using interception.dll for low-level input simulation.

## Features

- **Modern Dark UI**: Clean, professional dark theme interface
- **Key Press Simulation**: Press keys 0-9 using low-level interception
- **Mouse Click Simulation**: Left and right mouse clicks at specified coordinates
- **Customizable Delays**: Add delays between actions with millisecond precision
- **Macro Sequencing**: Create complex macro sequences with multiple actions
- **Loop Support**: Option to loop macros continuously
- **Real-time Status**: Live status updates and action highlighting during execution
- **Action Management**: Add, remove, reorder actions in your macro sequence

## Requirements

1. **Interception Driver**: You must install the interception driver first
   - Download from: https://github.com/oblitum/Interception
   - Follow the installation instructions
   - **Restart your PC** after installation

2. **interception.dll**: Place the interception.dll file in the same directory as the executable

## How to Use

### Adding Actions

1. **Key Press (0-9)**:
   - Select a number (0-9) in the "Key" field
   - Click "Add Key Press"

2. **Delay**:
   - Set the delay time in milliseconds (100-10000ms)
   - Click "Add Delay"

3. **Mouse Clicks**:
   - Set X and Y coordinates for the click position
   - Click "Add Mouse Left" or "Add Mouse Right"

### Managing Actions

- **Remove**: Select an action and click "Remove"
- **Reorder**: Use "Move Up" and "Move Down" to change action order
- **Clear All**: Remove all actions at once

### Running Macros

1. Add your desired actions to the list
2. Optionally check "Loop Macro" for continuous execution
3. Click "Start Macro" to begin
4. Click "Stop Macro" to halt execution

### Action List

The action list shows all your macro steps in order:
- Each action is numbered for easy reference
- Currently executing action is highlighted
- Actions display their type and parameters

## Key Mappings

The program uses scan codes for key presses:
- Key 0: Scan code 11
- Key 1: Scan code 2
- Key 2: Scan code 3
- Key 3: Scan code 4
- Key 4: Scan code 5
- Key 5: Scan code 6
- Key 6: Scan code 7
- Key 7: Scan code 8
- Key 8: Scan code 9
- Key 9: Scan code 10

## Example Macro Sequence

1. Add Key Press: 1
2. Add Delay: 1000ms
3. Add Mouse Left Click: (100, 100)
4. Add Delay: 500ms
5. Add Key Press: 2
6. Start Macro

This will:
1. Press the "1" key
2. Wait 1 second
3. Left-click at position (100, 100)
4. Wait 0.5 seconds
5. Press the "2" key

## Troubleshooting

- **"No device found" error**: Install the interception driver and restart your PC
- **Keys/clicks not working**: Ensure interception.dll is in the correct location
- **Permission issues**: Run as administrator if needed
- **Driver not loading**: Check if the interception driver service is running

## Safety Notes

- Test your macros carefully before using them in important applications
- Use reasonable delays to avoid overwhelming target applications
- The program uses low-level input injection, so it works with most applications
- Always have a way to stop the macro (Stop button or close the application)

## Technical Details

- Built with C# .NET Framework 4.8
- Uses Windows Forms for the UI
- Implements low-level input simulation via interception.dll
- Multi-threaded execution for responsive UI during macro playback
- Modern flat UI design with dark theme
